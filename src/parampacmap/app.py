from fastapi import Fast<PERSON><PERSON>, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import io, numpy as np, pandas as pd
from parampacmap import ParamPaCMAP

app = FastAPI()
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["POST"], allow_headers=["*"])
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10 MB

# ---------- helper: make every value JSON-safe ----------
def make_finite(obj):
    if isinstance(obj, (float, np.floating)):
        if np.isnan(obj):
            return None
        if np.isposinf(obj):
            return 1e6
        if np.isneginf(obj):
            return -1e6
        return float(obj)
    if isinstance(obj, (list, tuple, np.ndarray)):
        return [make_finite(x) for x in obj]
    if isinstance(obj, dict):
        return {k: make_finite(v) for k, v in obj.items()}
    return obj
# ---------------------------------------------------------

@app.post("/embed")
async def embed(
    upload: UploadFile = File(...),
    columns: str = Form(...),
    n_components: int = Form(2),
):
    raw = await upload.read()
    if len(raw) > MAX_FILE_SIZE:
        raise HTTPException(413, "File exceeds 10 MB limit")

    try:
        df = pd.read_csv(io.BytesIO(raw))
    except Exception as exc:
        raise HTTPException(400, f"CSV parse error: {exc}") from exc

    sel_cols = [c.strip() for c in columns.split(",") if c.strip()]
    if len(sel_cols) < max(2, n_components):
        raise HTTPException(400, f"Need ≥ {max(2, n_components)} numeric columns")
    missing = [c for c in sel_cols if c not in df.columns]
    if missing:
        raise HTTPException(400, f"Missing columns: {missing}")

    df_num = df[sel_cols].apply(pd.to_numeric, errors="coerce").dropna()
    if df_num.empty:
        raise HTTPException(400, "No valid numeric rows found")

    X = df_num.to_numpy(dtype=np.float32)

    try:
        emb = ParamPaCMAP(n_components=n_components).fit_transform(X)
    except Exception as exc:
        raise HTTPException(500, f"Embedding failed: {exc}") from exc

    emb = np.clip(np.nan_to_num(emb, nan=0.0, posinf=1e6, neginf=-1e6), -1e6, 1e6)
    metadata = make_finite(df.loc[df_num.index].to_dict("records"))

    return {"embedding": make_finite(emb.tolist()), "metadata": metadata}